CREATE TYPE "public"."chat_session_type" AS ENUM('RESUME_REVIEW', 'INTERVIEW_PREP', 'INTERVIEW_RESULTS', 'GENERAL');--> statement-breakpoint
CREATE TYPE "public"."difficulty_level" AS ENUM('INTRODUCTORY', 'BASIC', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');--> statement-breakpoint
CREATE TYPE "public"."experience_level" AS ENUM('ENTRY', 'JUNIOR', 'MID', 'SENIOR', 'LEAD', 'EXECUTIVE');--> statement-breakpoint
CREATE TYPE "public"."interview_status" AS ENUM('PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED');--> statement-breakpoint
CREATE TYPE "public"."interview_style" AS ENUM('FRIENDLY', 'NEUTRAL', 'CHALLENGING');--> statement-breakpoint
CREATE TYPE "public"."interview_type" AS ENUM('CODING', 'SYSTEM_DESIGN', 'TECHNICAL_CONCEPTS', 'BEHAVIORAL', 'CASE_STUDY', 'DOMAIN_SPECIFIC');--> statement-breakpoint
CREATE TYPE "public"."knowledge_source" AS ENUM('RESUME', 'LINKEDIN', 'FILE', 'NOTE', 'URL', 'EXTERNAL');--> statement-breakpoint
CREATE TYPE "public"."media_type" AS ENUM('AUDIO', 'VIDEO');--> statement-breakpoint
CREATE TYPE "public"."meeting_type" AS ENUM('AI_PRACTICE', 'LIVE_INTERVIEW', 'PEER_PRACTICE');--> statement-breakpoint
CREATE TYPE "public"."message_role" AS ENUM('USER', 'ASSISTANT', 'SYSTEM');--> statement-breakpoint
CREATE TYPE "public"."participant_role" AS ENUM('INTERVIEWER_AI', 'CANDIDATE', 'INTERVIEWER_HUMAN', 'PEER');--> statement-breakpoint
CREATE TYPE "public"."recording_status" AS ENUM('NOT_STARTED', 'RECORDING', 'PROCESSING', 'COMPLETED', 'FAILED');--> statement-breakpoint
CREATE TYPE "public"."resume_section_type" AS ENUM('SUMMARY', 'EXPERIENCE', 'EDUCATION', 'PROJECTS', 'SKILLS', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."skill_proficiency" AS ENUM('NOVICE', 'INTERMEDIATE', 'ADVANCED', 'EXPERT');--> statement-breakpoint
CREATE TYPE "public"."speaker_role" AS ENUM('INTERVIEWER', 'CANDIDATE', 'SYSTEM');--> statement-breakpoint
CREATE TYPE "public"."tool_call_type" AS ENUM('RETRIEVE', 'FUNCTION_CALL', 'WEB_SEARCH', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."user_role" AS ENUM('USER', 'ADMIN');--> statement-breakpoint
CREATE TABLE "accounts" (
	"id" text PRIMARY KEY NOT NULL,
	"account_id" text NOT NULL,
	"provider_id" text NOT NULL,
	"user_id" uuid NOT NULL,
	"access_token" text,
	"refresh_token" text,
	"id_token" text,
	"access_token_expires_at" timestamp with time zone,
	"refresh_token_expires_at" timestamp with time zone,
	"scope" text,
	"password" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sessions" (
	"id" text PRIMARY KEY NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"token" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"ip_address" text,
	"user_agent" text,
	"user_id" uuid NOT NULL,
	CONSTRAINT "sessions_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" text NOT NULL,
	"name" text NOT NULL,
	"bio" text,
	"role" "user_role" DEFAULT 'USER' NOT NULL,
	"email_verified" boolean NOT NULL,
	"image" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"stripe_customer_id" text,
	"password" text,
	"username" text NOT NULL,
	"display_username" text,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "verification" (
	"id" text PRIMARY KEY NOT NULL,
	"identifier" text NOT NULL,
	"value" text NOT NULL,
	"expires_at" timestamp with time zone NOT NULL,
	"created_at" timestamp with time zone,
	"updated_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "chat_context_items" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"title" text,
	"content" text,
	"source" "knowledge_source" DEFAULT 'NOTE' NOT NULL,
	"version" integer DEFAULT 1 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"metadata" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chat_message_context" (
	"message_id" uuid NOT NULL,
	"context_item_id" uuid NOT NULL,
	"relevance_score" real,
	CONSTRAINT "chat_message_context_pk" PRIMARY KEY("message_id","context_item_id")
);
--> statement-breakpoint
CREATE TABLE "chat_messages" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"session_id" uuid NOT NULL,
	"role" "message_role" NOT NULL,
	"content" text NOT NULL,
	"metadata" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chat_session_context" (
	"session_id" uuid NOT NULL,
	"context_item_id" uuid NOT NULL,
	"is_pinned" boolean DEFAULT false NOT NULL,
	"overrides" jsonb DEFAULT '{}'::jsonb NOT NULL,
	CONSTRAINT "chat_session_context_pk" PRIMARY KEY("session_id","context_item_id")
);
--> statement-breakpoint
CREATE TABLE "chat_sessions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"resume_id" uuid,
	"interview_id" uuid,
	"session_type" "chat_session_type" DEFAULT 'GENERAL' NOT NULL,
	"title" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "chat_tool_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"message_id" uuid NOT NULL,
	"tool_name" text NOT NULL,
	"call_type" "tool_call_type" DEFAULT 'OTHER' NOT NULL,
	"request" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"response" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"latency_ms" real,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "interview_feedback" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"overall_score" real,
	"communication_score" real,
	"technical_depth_score" real,
	"problem_solving_score" real,
	"domain_knowledge_score" real,
	"strengths" text[] DEFAULT '{}'::text[] NOT NULL,
	"weaknesses" text[] DEFAULT '{}'::text[] NOT NULL,
	"improvement_areas" text[] DEFAULT '{}'::text[] NOT NULL,
	"metrics" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"recommendations" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"rubric_version" text,
	"generated_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "interview_feedback_interview_id_unique" UNIQUE("interview_id")
);
--> statement-breakpoint
CREATE TABLE "interviews" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"resume_id" uuid NOT NULL,
	"title" text,
	"interview_type" "interview_type" NOT NULL,
	"job_title" text NOT NULL,
	"company_name" text NOT NULL,
	"industry_name" text NOT NULL,
	"experience_level" "experience_level" NOT NULL,
	"difficulty_level" "difficulty_level" NOT NULL,
	"interview_style" "interview_style" DEFAULT 'NEUTRAL' NOT NULL,
	"duration_minutes" integer DEFAULT 60 NOT NULL,
	"relevant_skills" text[] DEFAULT '{}'::text[] NOT NULL,
	"job_description" text,
	"key_responsibilities" text,
	"custom_instructions" text,
	"status" "interview_status" DEFAULT 'PENDING' NOT NULL,
	"session_data" jsonb,
	"room_id" text,
	"recording_url" text,
	"recording_status" "recording_status",
	"room_started_at" timestamp with time zone,
	"room_ended_at" timestamp with time zone,
	"meeting_type" "meeting_type" DEFAULT 'AI_PRACTICE' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "interview_media_assets" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"utterance_id" uuid,
	"media_type" "media_type" NOT NULL,
	"url" text NOT NULL,
	"duration_ms" integer,
	"format" text,
	"size_bytes" integer,
	"meta" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "interview_participants" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"role" "participant_role" NOT NULL,
	"identity" text NOT NULL,
	"joined_at" timestamp with time zone,
	"left_at" timestamp with time zone,
	"meta" jsonb DEFAULT '{}'::jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "interview_questions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"position" integer NOT NULL,
	"question_text" text NOT NULL,
	"ideal_answer" text,
	"answer_summary" text,
	"score" real,
	"rubric" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"started_at" timestamp with time zone,
	"ended_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "interview_room_events" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"event_type" text NOT NULL,
	"at" timestamp with time zone DEFAULT now() NOT NULL,
	"data" jsonb DEFAULT '{}'::jsonb NOT NULL
);
--> statement-breakpoint
CREATE TABLE "interview_skill_ratings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"skill_name" text NOT NULL,
	"score" real,
	"evidence" text
);
--> statement-breakpoint
CREATE TABLE "interview_utterances" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"interview_id" uuid NOT NULL,
	"question_id" uuid,
	"speaker" "speaker_role" NOT NULL,
	"transcript" text,
	"audio_url" text,
	"start_ms" integer NOT NULL,
	"end_ms" integer NOT NULL,
	"word_count" integer,
	"metrics" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "knowledge_chunks" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"document_id" uuid NOT NULL,
	"position" integer NOT NULL,
	"content" text NOT NULL,
	"hash" text NOT NULL,
	"token_count" integer,
	"external_vector_id" text,
	"meta" jsonb DEFAULT '{}'::jsonb NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "knowledge_chunks_hash_unique" UNIQUE("hash")
);
--> statement-breakpoint
CREATE TABLE "chat_message_citations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"message_id" uuid NOT NULL,
	"chunk_id" uuid NOT NULL,
	"quote" text,
	"score" real
);
--> statement-breakpoint
CREATE TABLE "knowledge_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"resume_id" uuid,
	"source" "knowledge_source" NOT NULL,
	"title" text,
	"url" text,
	"chunk_count" integer DEFAULT 0 NOT NULL,
	"external_vector_namespace" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "resume_documents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"title" text,
	"file_name" text NOT NULL,
	"file_url" text NOT NULL,
	"file_type" text NOT NULL,
	"file_size" integer NOT NULL,
	"raw_text" text,
	"ai_summary" text,
	"extracted_skills" text[] DEFAULT '{}'::text[] NOT NULL,
	"uploaded_at" timestamp with time zone DEFAULT now() NOT NULL,
	"processed_at" timestamp with time zone
);
--> statement-breakpoint
CREATE TABLE "resume_sections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"resume_id" uuid NOT NULL,
	"section_type" "resume_section_type" NOT NULL,
	"content" text NOT NULL,
	"position" integer NOT NULL
);
--> statement-breakpoint
CREATE TABLE "resume_skills" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"resume_id" uuid NOT NULL,
	"skill_name" text NOT NULL,
	"proficiency" "skill_proficiency",
	"years_experience" integer
);
--> statement-breakpoint
ALTER TABLE "accounts" ADD CONSTRAINT "accounts_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "sessions" ADD CONSTRAINT "sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_context_items" ADD CONSTRAINT "chat_context_items_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_message_context" ADD CONSTRAINT "chat_message_context_message_id_chat_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_message_context" ADD CONSTRAINT "chat_message_context_context_item_id_chat_context_items_id_fk" FOREIGN KEY ("context_item_id") REFERENCES "public"."chat_context_items"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_messages" ADD CONSTRAINT "chat_messages_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."chat_sessions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_session_context" ADD CONSTRAINT "chat_session_context_session_id_chat_sessions_id_fk" FOREIGN KEY ("session_id") REFERENCES "public"."chat_sessions"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_session_context" ADD CONSTRAINT "chat_session_context_context_item_id_chat_context_items_id_fk" FOREIGN KEY ("context_item_id") REFERENCES "public"."chat_context_items"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_resume_id_resume_documents_id_fk" FOREIGN KEY ("resume_id") REFERENCES "public"."resume_documents"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_tool_events" ADD CONSTRAINT "chat_tool_events_message_id_chat_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_feedback" ADD CONSTRAINT "interview_feedback_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interviews" ADD CONSTRAINT "interviews_resume_id_resume_documents_id_fk" FOREIGN KEY ("resume_id") REFERENCES "public"."resume_documents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_media_assets" ADD CONSTRAINT "interview_media_assets_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_media_assets" ADD CONSTRAINT "interview_media_assets_utterance_id_interview_utterances_id_fk" FOREIGN KEY ("utterance_id") REFERENCES "public"."interview_utterances"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_participants" ADD CONSTRAINT "interview_participants_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_questions" ADD CONSTRAINT "interview_questions_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_room_events" ADD CONSTRAINT "interview_room_events_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_skill_ratings" ADD CONSTRAINT "interview_skill_ratings_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_utterances" ADD CONSTRAINT "interview_utterances_interview_id_interviews_id_fk" FOREIGN KEY ("interview_id") REFERENCES "public"."interviews"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "interview_utterances" ADD CONSTRAINT "interview_utterances_question_id_interview_questions_id_fk" FOREIGN KEY ("question_id") REFERENCES "public"."interview_questions"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "knowledge_chunks" ADD CONSTRAINT "knowledge_chunks_document_id_knowledge_documents_id_fk" FOREIGN KEY ("document_id") REFERENCES "public"."knowledge_documents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_message_citations" ADD CONSTRAINT "chat_message_citations_message_id_chat_messages_id_fk" FOREIGN KEY ("message_id") REFERENCES "public"."chat_messages"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "chat_message_citations" ADD CONSTRAINT "chat_message_citations_chunk_id_knowledge_chunks_id_fk" FOREIGN KEY ("chunk_id") REFERENCES "public"."knowledge_chunks"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "knowledge_documents" ADD CONSTRAINT "knowledge_documents_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "knowledge_documents" ADD CONSTRAINT "knowledge_documents_resume_id_resume_documents_id_fk" FOREIGN KEY ("resume_id") REFERENCES "public"."resume_documents"("id") ON DELETE set null ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "resume_documents" ADD CONSTRAINT "resume_documents_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "resume_sections" ADD CONSTRAINT "resume_sections_resume_id_resume_documents_id_fk" FOREIGN KEY ("resume_id") REFERENCES "public"."resume_documents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
ALTER TABLE "resume_skills" ADD CONSTRAINT "resume_skills_resume_id_resume_documents_id_fk" FOREIGN KEY ("resume_id") REFERENCES "public"."resume_documents"("id") ON DELETE cascade ON UPDATE cascade;--> statement-breakpoint
CREATE INDEX "idx_accounts_user" ON "accounts" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "uniq_provider_account" ON "accounts" USING btree ("provider_id","account_id");--> statement-breakpoint
CREATE INDEX "idx_sessions_user_expires" ON "sessions" USING btree ("user_id","expires_at");--> statement-breakpoint
CREATE INDEX "idx_context_items_user_active" ON "chat_context_items" USING btree ("user_id","is_active");--> statement-breakpoint
CREATE INDEX "idx_chat_messages_session_time" ON "chat_messages" USING btree ("session_id","created_at");--> statement-breakpoint
CREATE INDEX "idx_session_context_pinned" ON "chat_session_context" USING btree ("is_pinned");--> statement-breakpoint
CREATE INDEX "idx_chat_sessions_user" ON "chat_sessions" USING btree ("user_id","created_at");--> statement-breakpoint
CREATE INDEX "idx_chat_sessions_resume" ON "chat_sessions" USING btree ("resume_id");--> statement-breakpoint
CREATE INDEX "idx_chat_sessions_interview" ON "chat_sessions" USING btree ("interview_id");--> statement-breakpoint
CREATE INDEX "idx_tool_events_message" ON "chat_tool_events" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "idx_interviews_user" ON "interviews" USING btree ("user_id","created_at");--> statement-breakpoint
CREATE INDEX "idx_interviews_resume" ON "interviews" USING btree ("resume_id");--> statement-breakpoint
CREATE INDEX "idx_interviews_status" ON "interviews" USING btree ("status","created_at");--> statement-breakpoint
CREATE INDEX "idx_media_interview" ON "interview_media_assets" USING btree ("interview_id");--> statement-breakpoint
CREATE INDEX "idx_media_utterance" ON "interview_media_assets" USING btree ("utterance_id");--> statement-breakpoint
CREATE INDEX "idx_interview_participants_interview" ON "interview_participants" USING btree ("interview_id");--> statement-breakpoint
CREATE INDEX "idx_interview_questions_interview_pos" ON "interview_questions" USING btree ("interview_id","position");--> statement-breakpoint
CREATE INDEX "idx_room_events_interview_at" ON "interview_room_events" USING btree ("interview_id","at");--> statement-breakpoint
CREATE INDEX "idx_skill_ratings_interview_skill" ON "interview_skill_ratings" USING btree ("interview_id","skill_name");--> statement-breakpoint
CREATE INDEX "idx_utterances_interview_time" ON "interview_utterances" USING btree ("interview_id","start_ms");--> statement-breakpoint
CREATE INDEX "idx_utterances_question" ON "interview_utterances" USING btree ("question_id");--> statement-breakpoint
CREATE INDEX "idx_kchunks_document_pos" ON "knowledge_chunks" USING btree ("document_id","position");--> statement-breakpoint
CREATE INDEX "idx_kchunks_vector_id" ON "knowledge_chunks" USING btree ("external_vector_id");--> statement-breakpoint
CREATE INDEX "idx_citations_message" ON "chat_message_citations" USING btree ("message_id");--> statement-breakpoint
CREATE INDEX "idx_citations_chunk" ON "chat_message_citations" USING btree ("chunk_id");--> statement-breakpoint
CREATE INDEX "idx_kdocs_user" ON "knowledge_documents" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_kdocs_resume" ON "knowledge_documents" USING btree ("resume_id");--> statement-breakpoint
CREATE INDEX "idx_kdocs_namespace" ON "knowledge_documents" USING btree ("external_vector_namespace");--> statement-breakpoint
CREATE INDEX "idx_resume_user" ON "resume_documents" USING btree ("user_id");--> statement-breakpoint
CREATE INDEX "idx_resume_sections_resume_pos" ON "resume_sections" USING btree ("resume_id","position");--> statement-breakpoint
CREATE INDEX "idx_resume_skills_resume_name" ON "resume_skills" USING btree ("resume_id","skill_name");