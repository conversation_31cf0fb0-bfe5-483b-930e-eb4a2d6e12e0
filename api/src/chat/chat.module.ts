import { Modu<PERSON> } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { ChatController } from "./chat.controller";
import { ChatService } from "./chat.service";
import { DrizzleModule } from "src/db/drizzle.module";

@Module({
  imports: [
    DrizzleModule,
    ClientsModule.register([
      {
        name: "AI_SERVICE",
        transport: Transport.REDIS,
        options: {
          host: process.env.REDIS_HOST || "localhost",
          port: parseInt(process.env.REDIS_PORT || "6379"),
        },
      },
    ]),
  ],
  controllers: [ChatController],
  providers: [ChatService],
  exports: [ChatService],
})
export class ChatModule {}
