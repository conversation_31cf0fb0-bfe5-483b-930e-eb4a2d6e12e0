import { Injectable } from "@nestjs/common";
import { Inject } from "@nestjs/common";
import { DRIZZLE } from "src/db/drizzle.module";

export interface Conversation {
  id: string;
  userId: string;
  createdAt: Date;
  messages: Message[];
}

export interface Message {
  id: string;
  conversationId: string;
  content: string;
  role: "user" | "assistant";
  tokens?: number;
  createdAt: Date;
}

@Injectable()
export class ChatService {
  // In-memory storage for demo (replace with real database)
  private conversations: Map<string, Conversation> = new Map();
  private messages: Map<string, Message[]> = new Map();

  async saveUserMessage(
    userId: string,
    message: string,
  ): Promise<Conversation> {
    const conversationId = this.generateId();

    const conversation: Conversation = {
      id: conversationId,
      userId,
      createdAt: new Date(),
      messages: [],
    };

    const userMessage: Message = {
      id: this.generateId(),
      conversationId,
      content: message,
      role: "user",
      createdAt: new Date(),
    };

    this.conversations.set(conversationId, conversation);
    this.messages.set(conversationId, [userMessage]);

    console.log(`💾 Saved user message for conversation ${conversationId}`);
    return conversation;
  }

  async saveAiResponse(
    conversationId: string,
    response: string,
    tokens: number,
  ): Promise<void> {
    const aiMessage: Message = {
      id: this.generateId(),
      conversationId,
      content: response,
      role: "assistant",
      tokens,
      createdAt: new Date(),
    };

    const existingMessages = this.messages.get(conversationId) || [];
    existingMessages.push(aiMessage);
    this.messages.set(conversationId, existingMessages);

    console.log(`🤖 Saved AI response for conversation ${conversationId}`);
  }

  async getChatHistory(conversationId: string): Promise<Message[]> {
    return this.messages.get(conversationId) || [];
  }

  async saveMessage(data: any): Promise<void> {
    // Generic message saving for microservice calls
    console.log("Saving message via microservice:", data);
  }

  private generateId(): string {
    return `${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }
}
