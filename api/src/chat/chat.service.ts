import { Injectable } from "@nestjs/common";
import { Inject } from "@nestjs/common";
import { ClientProxy } from "@nestjs/microservices";
import { DRIZZLE } from "src/db/drizzle.module";
import type { DrizzleDB } from "src/db/types/drizzle";
import { chatSessions, chatMessages } from "src/db/schema";
import { sql, eq } from "drizzle-orm";

@Injectable()
export class ChatService {
  constructor(
    @Inject("AI_SERVICE") private readonly aiClient: ClientProxy,
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
  ) {}

  async saveUserMessage(userId: string, message: string): Promise<any> {
    const [session] = await this.db
      .insert(chatSessions.chatSessions)
      .values({
        userId,
        sessionType: "GENERAL",
        title: message.substring(0, 50) + "...",
      })
      .returning();

    console.log(`💾 Saved user message for session ${session.id}`);
    return {
      id: session.id,
      userId: session.userId,
      createdAt: session.createdAt,
    };
  }

  async saveAiResponse(
    sessionId: string,
    response: string,
    tokens?: number,
  ): Promise<void> {
    await this.db.insert(chatMessages.chatMessages).values({
      sessionId,
      role: "ASSISTANT",
      content: response,
      metadata: tokens ? { tokens } : {},
    });
  }

  async getChatHistory(sessionId: string): Promise<any[]> {
    const messages = await this.db
      .select()
      .from(chatMessages.chatMessages)
      .where(eq(chatMessages.chatMessages.sessionId, sessionId))
      .orderBy(chatMessages.chatMessages.createdAt);

    return messages;
  }

  async testConnection(): Promise<number> {
    const result = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(chatSessions.chatSessions);

    console.log(`📊 Total chat sessions: ${result[0].count}`);
    return result[0].count;
  }
}
