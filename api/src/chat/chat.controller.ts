import { <PERSON>, Post, Get, Body, Param, Inject } from "@nestjs/common";
import { ClientProxy, MessagePattern, Payload } from "@nestjs/microservices";
import { ChatService } from "./chat.service";

export interface CreateChatRequest {
  userId: string;
  message: string;
}

@Controller("chat")
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    @Inject("AI_SERVICE") private readonly aiClient: ClientProxy,
  ) {}

  // HTTP endpoint for frontend
  @Post()
  async createChat(@Body() request: CreateChatRequest) {
    console.log("💬 Chat request received:", request);

    // Save user message to database
    const conversation = await this.chatService.saveUserMessage(
      request.userId,
      request.message,
    );

    // Send to AI service via microservice
    const aiResponse = await this.aiClient
      .send("ai.chat", {
        message: request.message,
        userId: request.userId,
        conversationId: conversation.id,
      })
      .toPromise();

    // Save AI response to database
    await this.chatService.saveAiResponse(
      conversation.id,
      aiResponse.response,
      aiResponse.tokens,
    );

    return {
      conversationId: conversation.id,
      userMessage: request.message,
      aiResponse: aiResponse.response,
      tokens: aiResponse.tokens,
    };
  }

  @Get(":conversationId/history")
  async getChatHistory(@Param("conversationId") conversationId: string) {
    return await this.chatService.getChatHistory(conversationId);
  }

  @Get("test")
  async testDatabase() {
    const count = await this.chatService.testConnection();
    return { message: "Database connected!", sessionCount: count };
  }
}
