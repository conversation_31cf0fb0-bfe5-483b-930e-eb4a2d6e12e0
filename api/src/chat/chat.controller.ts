import { <PERSON>, Post, Get, Body, Param, Inject } from "@nestjs/common";
import { ClientProxy, MessagePattern, Payload } from "@nestjs/microservices";
import { ChatService } from "./chat.service";
import { DRIZZLE } from "src/db/drizzle.module";
import type { DrizzleDB } from "src/db/types/drizzle";
import { users } from "src/db/schema";
import { eq } from "drizzle-orm";

export interface CreateChatRequest {
  userId: string;
  message: string;
}

@Controller("chat")
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    @Inject("AI_SERVICE") private readonly aiClient: ClientProxy,
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
  ) {}

  // HTTP endpoint for frontend
  @Post()
  async createChat(@Body() request: CreateChatRequest) {
    console.log("💬 Chat request received:", request);

    // Save user message to database
    const conversation = await this.chatService.saveUserMessage(
      request.userId,
      request.message,
    );

    // Send to AI service via microservice
    const aiResponse = await this.aiClient
      .send("ai.chat", {
        message: request.message,
        userId: request.userId,
        conversationId: conversation.id,
      })
      .toPromise();

    // Save AI response to database
    await this.chatService.saveAiResponse(
      conversation.id,
      aiResponse.response,
      aiResponse.tokens,
    );

    return {
      conversationId: conversation.id,
      userMessage: request.message,
      aiResponse: aiResponse.response,
      tokens: aiResponse.tokens,
    };
  }

  @Get(":conversationId/history")
  async getChatHistory(@Param("conversationId") conversationId: string) {
    return await this.chatService.getChatHistory(conversationId);
  }

  @Get("test")
  async testDatabase() {
    const count = await this.chatService.testConnection();
    return { message: "Database connected!", sessionCount: count };
  }

  @Post("create-test-user")
  async createTestUser() {
    try {
      // Import users schema at the top of the file
      const [user] = await this.db
        .insert(users.users)
        .values({
          email: "<EMAIL>",
          name: "Test User",
          emailVerified: false,
          username: "testuser",
        })
        .returning();

      return { message: "Test user created!", userId: user.id };
    } catch (error) {
      // If user already exists, find and return existing user
      const [existingUser] = await this.db
        .select()
        .from(users.users)
        .where(eq(users.users.email, "<EMAIL>"))
        .limit(1);

      if (existingUser) {
        return {
          message: "Test user already exists!",
          userId: existingUser.id,
        };
      }

      throw error;
    }
  }
}
