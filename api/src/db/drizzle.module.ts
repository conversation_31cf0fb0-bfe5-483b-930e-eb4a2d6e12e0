import { drizzle, NodePgDatabase } from "drizzle-orm/node-postgres";
import * as schema from "./schema";
import type { DrizzleDB } from "./types/drizzle";
import { Module } from "@nestjs/common";
import { Pool } from "pg";
import { ConfigService } from "@nestjs/config";

export const DRIZZLE = Symbol("DRIZZLE");
@Module({
  providers: [
    {
      provide: DRIZZLE,
      useFactory: async (configService: ConfigService) => {
        const databaseUrl = configService.get<string>("DATABASE_URL");
        if (!databaseUrl) {
          throw new Error("DATABASE_URL is required!");
        }

        const pool = new Pool({ connectionString: databaseUrl, ssl: true });
        return drizzle(pool, { schema }) as DrizzleDB;
      },
      inject: [ConfigService],
    },
  ],
  exports: [DRIZZLE],
})
export class DrizzleModule {}
