import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { mediaType } from "../auth/enums";
import { interviews } from "./interviews.schema";
import { interviewUtterances } from "./utterances.schema";

export const interviewMediaAssets = pgTable(
  "interview_media_assets",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    interviewId: uuid("interview_id")
      .notNull()
      .references(() => interviews.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    utteranceId: uuid("utterance_id").references(() => interviewUtterances.id, {
      onDelete: "set null",
      onUpdate: "cascade",
    }),
    mediaType: mediaType("media_type").notNull(),
    url: text("url").notNull(),
    durationMs: integer("duration_ms"),
    format: text("format"),
    sizeBytes: integer("size_bytes"), // int8 use bigints if needed
    meta: jsonb("meta")
      .notNull()
      .default(sql`'{}'::jsonb`),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    interviewIdx: index("idx_media_interview").on(t.interviewId),
    utteranceIdx: index("idx_media_utterance").on(t.utteranceId),
  }),
);
