import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { speakerRole } from "../auth/enums";
import { interviews } from "./interviews.schema";
import { interviewQuestions } from "./questions.schema";

export const interviewUtterances = pgTable(
  "interview_utterances",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    interviewId: uuid("interview_id")
      .notNull()
      .references(() => interviews.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    questionId: uuid("question_id").references(() => interviewQuestions.id, {
      onDelete: "set null",
      onUpdate: "cascade",
    }),
    speaker: speakerRole("speaker").notNull(),
    transcript: text("transcript"),
    audioUrl: text("audio_url"),
    startMs: integer("start_ms").notNull(),
    endMs: integer("end_ms").notNull(),
    wordCount: integer("word_count"),
    metrics: jsonb("metrics")
      .notNull()
      .default(sql`'{}'::jsonb`),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
    // FTS tsvector should be added via migration for generated column
  },
  (t) => ({
    interviewTimeIdx: index("idx_utterances_interview_time").on(
      t.interviewId,
      t.startMs,
    ),
    questionIdx: index("idx_utterances_question").on(t.questionId),
  }),
);
