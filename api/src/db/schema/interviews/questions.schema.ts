import {
  pgTable,
  uuid,
  text,
  integer,
  index,
  real,
  jsonb,
  timestamp,
} from "drizzle-orm/pg-core";
import { interviews } from "./interviews.schema";
import { sql } from "drizzle-orm";

export const interviewQuestions = pgTable(
  "interview_questions",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    interviewId: uuid("interview_id")
      .notNull()
      .references(() => interviews.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    position: integer("position").notNull(),
    questionText: text("question_text").notNull(),
    idealAnswer: text("ideal_answer"),
    answerSummary: text("answer_summary"),
    score: real("score"),
    rubric: jsonb("rubric")
      .notNull()
      .default(sql`'{}'::jsonb`),
    startedAt: timestamp("started_at", { withTimezone: true }),
    endedAt: timestamp("ended_at", { withTimezone: true }),
  },
  (t) => ({
    interviewPosIdx: index("idx_interview_questions_interview_pos").on(
      t.interviewId,
      t.position,
    ),
  }),
);
