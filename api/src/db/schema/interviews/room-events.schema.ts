import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { interviews } from "./interviews.schema";

export const interviewRoomEvents = pgTable(
  "interview_room_events",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    interviewId: uuid("interview_id")
      .notNull()
      .references(() => interviews.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    eventType: text("event_type").notNull(),
    at: timestamp("at", { withTimezone: true }).notNull().defaultNow(),
    data: jsonb("data")
      .notNull()
      .default(sql`'{}'::jsonb`),
  },
  (t) => ({
    interviewAtIdx: index("idx_room_events_interview_at").on(
      t.interviewId,
      t.at,
    ),
  }),
);
