import { sql } from "drizzle-orm";
import { pgTable, uuid, text, real, index } from "drizzle-orm/pg-core";
import { interviews } from "./interviews.schema";

export const interviewSkillRatings = pgTable(
  "interview_skill_ratings",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    interviewId: uuid("interview_id")
      .notNull()
      .references(() => interviews.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    skillName: text("skill_name").notNull(),
    score: real("score"),
    evidence: text("evidence"),
  },
  (t) => ({
    interviewSkillIdx: index("idx_skill_ratings_interview_skill").on(
      t.interviewId,
      t.skillName,
    ),
  }),
);
