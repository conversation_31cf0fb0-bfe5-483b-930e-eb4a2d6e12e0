import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  boolean,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { knowledgeSource } from "../auth/enums";
import { users } from "../auth/users.schema";

export const chatContextItems = pgTable(
  "chat_context_items",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),
    title: text("title"),
    content: text("content"),
    source: knowledgeSource("source").notNull().default("NOTE"),
    version: integer("version").notNull().default(1),
    isActive: boolean("is_active").notNull().default(true),
    metadata: jsonb("metadata")
      .notNull()
      .default(sql`'{}'::jsonb`),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
    updatedAt: timestamp("updated_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    userActiveIdx: index("idx_context_items_user_active").on(
      t.userId,
      t.isActive,
    ),
  }),
);
