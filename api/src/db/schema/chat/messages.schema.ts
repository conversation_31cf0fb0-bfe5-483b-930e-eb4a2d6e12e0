import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  jsonb,
  index,
} from "drizzle-orm/pg-core";
import { messageRole } from "../auth/enums";
import { chatSessions } from "./sessions.schema";

export const chatMessages = pgTable(
  "chat_messages",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    sessionId: uuid("session_id")
      .notNull()
      .references(() => chatSessions.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    role: messageRole("role").notNull(),
    content: text("content").notNull(),
    metadata: jsonb("metadata")
      .notNull()
      .default(sql`'{}'::jsonb`),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
    // FTS tsvector via migration
  },
  (t) => ({
    sessionTimeIdx: index("idx_chat_messages_session_time").on(
      t.sessionId,
      t.createdAt,
    ),
  }),
);
