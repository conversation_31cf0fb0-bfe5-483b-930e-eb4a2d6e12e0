import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  index,
} from "drizzle-orm/pg-core";
import { knowledgeSource } from "../auth/enums";
import { users } from "../auth/users.schema";
import { resumeDocuments } from "../resume/documents.schema";

export const knowledgeDocuments = pgTable(
  "knowledge_documents",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),
    resumeId: uuid("resume_id").references(() => resumeDocuments.id, {
      onDelete: "set null",
      onUpdate: "cascade",
    }),
    source: knowledgeSource("source").notNull(),
    title: text("title"),
    url: text("url"),
    chunkCount: integer("chunk_count").notNull().default(0),
    externalVectorNamespace: text("external_vector_namespace"),
    createdAt: timestamp("created_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
  },
  (t) => ({
    userIdx: index("idx_kdocs_user").on(t.userId),
    resumeIdx: index("idx_kdocs_resume").on(t.resumeId),
    namespaceIdx: index("idx_kdocs_namespace").on(t.externalVectorNamespace),
  }),
);
