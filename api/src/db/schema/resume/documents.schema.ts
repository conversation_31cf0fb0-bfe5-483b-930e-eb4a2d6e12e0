import { sql } from "drizzle-orm";
import {
  pgTable,
  uuid,
  text,
  timestamp,
  integer,
  index,
} from "drizzle-orm/pg-core";
import { users } from "../auth/users.schema";

export const resumeDocuments = pgTable(
  "resume_documents",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    userId: uuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade", onUpdate: "cascade" }),
    title: text("title"),
    fileName: text("file_name").notNull(),
    fileUrl: text("file_url").notNull(),
    fileType: text("file_type").notNull(),
    fileSize: integer("file_size").notNull(),
    rawText: text("raw_text"),
    aiSummary: text("ai_summary"),
    extractedSkills: text("extracted_skills")
      .array()
      .notNull()
      .default(sql`'{}'::text[]`),
    uploadedAt: timestamp("uploaded_at", { withTimezone: true })
      .notNull()
      .defaultNow(),
    processedAt: timestamp("processed_at", { withTimezone: true }),
    // Optional: FTS tsvector column should be added via migration for generated column
  },
  (t) => ({
    userIdx: index("idx_resume_user").on(t.userId),
  }),
);
