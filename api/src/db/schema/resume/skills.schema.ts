import { pgTable, uuid, text, integer, index } from "drizzle-orm/pg-core";
import { resumeDocuments } from "./documents.schema";
import { sql } from "drizzle-orm";
import { skillProficiency } from "../auth/enums";

export const resumeSkills = pgTable(
  "resume_skills",
  {
    id: uuid("id")
      .primaryKey()
      .default(sql`gen_random_uuid()`),
    resumeId: uuid("resume_id")
      .notNull()
      .references(() => resumeDocuments.id, {
        onDelete: "cascade",
        onUpdate: "cascade",
      }),
    skillName: text("skill_name").notNull(),
    proficiency: skillProficiency("proficiency"),
    yearsExperience: integer("years_experience"),
  },
  (t) => ({
    resumeNameIdx: index("idx_resume_skills_resume_name").on(
      t.resumeId,
      t.skillName,
    ),
  }),
);
