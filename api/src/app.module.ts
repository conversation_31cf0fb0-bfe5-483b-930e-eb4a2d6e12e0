import { <PERSON>du<PERSON> } from "@nestjs/common";
import { AppController } from "./app.controller";
import { AppService } from "./app.service";
import { ChatModule } from "./chat/chat.module";
import { DrizzleModule } from "./db/drizzle.module";
import { ConfigModule } from "@nestjs/config";

@Module({
  imports: [
    DrizzleModule,
    ChatModule,
    ConfigModule.forRoot({ isGlobal: true }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
